package com.zhelian.model.yingxiaoyun.form.output;


import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;

public class YXYSaleBillListOutPutForm {

    @J<PERSON>NField(name = "BillID")
    public String BillID;
    @J<PERSON><PERSON><PERSON>(name = "BillType")
    public String BillType;
    @J<PERSON><PERSON>ield(name = "BillStatus")
    public String BillStatus;
    @J<PERSON><PERSON>ield(name = "BillDate")
    public String BillDate;
    @JSONField(name = "Amount")
    public BigDecimal Amount;
    @JSONField(name = "PayAmount")
    public BigDecimal PayAmount;


}
