package com.zhelian.model.yingxiaoyun.form.input;


import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.math.BigDecimal;

public class YXYInputForm implements Serializable {

    @J<PERSON><PERSON>ield(name = "EnterpriseCode")
    public String EnterpriseCode;
    @J<PERSON><PERSON>ield(name = "TenantCode")
    public String TenantCode;
    @JSONField(name = "EntityID")
    public int EntityID;
    @JSONField(name = "EntityName")
    public String EntityName;
    @JSONField(name = "Address")
    public String Address;
    @JSONField(name = "Longitude")
    public BigDecimal Longitude;
    @J<PERSON><PERSON>ield(name = "Latitude")
    public BigDecimal Latitude;
    @J<PERSON><PERSON><PERSON>(name = "Status")
    public String Status;
}
