package com.zhelian.model.yingxiaoyun.form.output;


import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class YXYFollowUpListOutPutForm {

    @JSONField(name = "FollowUpRecordID")
    public Integer FollowUpRecordID;
    @J<PERSON><PERSON>ield(name = "FollowUpContent")
    public String FollowUpContent;
    @JSONField(name = "FollowUpOn")
    public String FollowUpOn;
    @JSONField(name = "FollowUpBy")
    public String FollowUpBy;
    @JSONField(name = "CreatedOn")
    public String CreatedOn;
    @JSONField(name = "Attachment")
    public List<YXYAttachmentOutPutForm> Attachment;

}
