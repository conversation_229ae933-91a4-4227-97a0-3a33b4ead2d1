package com.zhelian.model.yingxiaoyun.form.output;


import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class YXYCallBackOutListProjectPutForm {

    @JSONField(name = "ProjectName")
    public String ProjectName;
    @J<PERSON><PERSON>ield(name = "CallbackCycle")
    public Integer CallbackCycle;
    @JSONField(name = "CallbackRemark")
    public String CallbackRemark;
    @JSONField(name = "CallbackContent")
    public String CallbackContent;
    @JSONField(name = "Attachment")
    public List<YXYAttachmentOutPutForm> Attachment;

}
