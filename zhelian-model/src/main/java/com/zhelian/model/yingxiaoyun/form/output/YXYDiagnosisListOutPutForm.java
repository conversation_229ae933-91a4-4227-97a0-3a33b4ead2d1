package com.zhelian.model.yingxiaoyun.form.output;


import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class YXYDiagnosisListOutPutForm {

    @JSONField(name = "DiagnosisRecordID")
    public Integer DiagnosisRecordID;
    @J<PERSON>NField(name = "DiagnosisContent")
    public String DiagnosisContent;
    @JSONField(name = "DiagnosisOn")
    public String DiagnosisOn;
    @JSONField(name = "DiagnosisBy")
    public String DiagnosisBy;
    @JSONField(name = "CreatedOn")
    public String CreatedOn;
    @JSONField(name = "Attachment")
    public List<YXYAttachmentOutPutForm> Attachment;

}
