package com.zhelian.model.yingxiaoyun.form.output;

import com.alibaba.fastjson.annotation.JSONField;

public class YXYRecordOutPutForm {
    @J<PERSON><PERSON>ield(name = "EnterpriseCode")
    public String EnterpriseCode;
    @J<PERSON><PERSON>ield(name = "TenantCode")
    public String TenantCode;
    @J<PERSON><PERSON>ield(name = "AccountPhone")
    public String AccountPhone;
    @JSONField(name = "BeginTime")
    public String BeginTime;
    @JSONField(name = "EndTime")
    public String EndTime;
    @JSONField(name = "Type")
    public Integer Type;
    @J<PERSON>NField(name = "PageIndex")
    public Integer PageIndex;
    @J<PERSON><PERSON><PERSON>(name = "PageSize")
    public Integer PageSize;


}
