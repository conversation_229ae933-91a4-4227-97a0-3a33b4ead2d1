package com.zhelian.model.miniprogram.form.output;


import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;

public class MiniProjectOnlyOutputForm {

    @JSONField(name = "ID")
    public Integer ID;
    @J<PERSON>NField(name = "Name")
    public String Name;
    @JSONField(name = "Alias")
    public String Alias;
    @JSONField(name = "ParentID")
    public Integer ParentID;
    @JSONField(name = "TreatTime")
    public Integer TreatTime;
    @JSONField(name = "IsProject")
    public Boolean IsProject;
    @JSONField(name = "Price")
    public BigDecimal Price;

}
