package com.zhelian.model.miniprogram.form.input;


import com.zhelian.model.base.BasePage;

public class MiniCustomerQueryForm extends BasePage {

    public String Name;
    public String EmployeeID;
    public String StartTime;
    public String EndTime;
    public String CustomerStatus;
    public Integer CustomerLevelID;
    public Integer CustomerSourceID;
    public String StartDate;
    public String EndDate;
    public Integer ServicerID;
    public String ServicerEmployeeID;
    public String ToShopStartDate;
    public String ToShopEndDate;
    public Integer ChannelID;
    public String MemberOnStartDate;
    public String MemberOnEndDate;
    public String ChannelName;
}
