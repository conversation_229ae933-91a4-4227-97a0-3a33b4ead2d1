package com.zhelian.model.miniprogram.form.output;


import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class MiniCategoryAndProjectOutputForm {

    @JSONField(name = "ID")
    public Integer ID;
    @J<PERSON><PERSON>ield(name = "Name")
    public String Name;
    @J<PERSON><PERSON>ield(name = "ParentID")
    public Integer ParentID;
    @JSONField(name = "IsProject")
    public Boolean IsProject;
    @JSONField(name = "Child")
    public List<MiniProjectOnlyOutputForm> Child;

}
