package com.zhelian.model.miniprogram.form.output;


import java.math.BigDecimal;

public class CustomerNumberDetailRateOutputForm {

    public BigDecimal CurrentMonthNewCustomer;
    public BigDecimal NewCustomerRate;
    public BigDecimal CurrentMonthNewCustomerRetained;
    public BigDecimal NewCustomerRetainedRate;
    public BigDecimal CurrentMonthActiveOldCustomer;
    public BigDecimal ActiveOldCustomerRate;
    public BigDecimal CurrentMonthActiveOldCustomerRetained;
    public BigDecimal ActiveOldCustomerRetainedRate;
    public BigDecimal CurrentMonthOldAndNewSource;
    public BigDecimal OldAndNewSourceRate;
    public BigDecimal CurrentMonthOldAndNewSourceRetained;
    public BigDecimal OldAndNewSourceRetainedRate;
    public BigDecimal CurrentMonthHighQualityCustomer;
    public BigDecimal HighQualityCustomerRetainedRate;

}
