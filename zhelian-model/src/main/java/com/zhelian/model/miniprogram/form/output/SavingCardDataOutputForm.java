package com.zhelian.model.miniprogram.form.output;

import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;
import java.util.List;

public class SavingCardDataOutputForm {

    @J<PERSON><PERSON><PERSON>(name = "ID")
    public Integer ID;
    @J<PERSON><PERSON>ield(name = "ParentID")
    public Integer ParentID = 0;
    @JSONField(name = "Name")
    public String Name;
    @JSONField(name = "Amount")
    public BigDecimal Amount;
    @JSONField(name = "LargessAmount")
    public BigDecimal LargessAmount;
    @J<PERSON>NField(name = "AmountUsed")
    public BigDecimal AmountUsed;
    @J<PERSON><PERSON>ield(name = "LargessAmountUsed")
    public BigDecimal LargessAmountUsed;
    @JSONField(name = "AmountBalance")
    public BigDecimal AmountBalance;
    @J<PERSON>NField(name = "LargessAmountBalance")
    public BigDecimal LargessAmountBalance;
    @J<PERSON>NField(name = "Child")
    public List<SavingCardChildDataOutputForm> Child;

}
