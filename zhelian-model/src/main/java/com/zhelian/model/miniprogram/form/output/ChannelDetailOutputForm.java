package com.zhelian.model.miniprogram.form.output;

import com.zhelian.model.CRM.form.output.*;

import java.math.BigDecimal;
import java.util.List;

public class ChannelDetailOutputForm {

    public Integer ID;

    public String Name;

    public Integer ChannelTypeID;

    public String ChannelTypeName;

    public Integer ChannelLevelID;

    public String ChannelLevelName;

    public List<ChannelDeveloperOutputForm> Developer;

    public List<ChannelConsultantOutputForm> Consultant;

    public Integer ParentID;

    public String ParentName;

    public String ContactPersonName;

    public String ContactPersonMobile;

    public String ContactPersonIDNumber;

    public String ProvinceCode;

    public String CityCode;

    public String AreaCode;

    public String ProvinceName;

    public String CityName;

    public String AreaName;

    public String AddressDetail;

    public String Remark;

    public Integer Sequence;

    public Boolean Active;

    public String ApprovalStatus;

    public BigDecimal Longitude;

    public BigDecimal Latitude;

    public List<IntroducerListOutputForm> IntroducerList;

    public List<ChannelCompanyOutputForm> Company;

    public List<ChannelContractOutputForm> Contract;

    public List<ChannelVisitDetailOutputForm> ChannelVisit;

    public String CreatedBy;

    public String CreatedOn;

    public String ApprovalRemark;
}
