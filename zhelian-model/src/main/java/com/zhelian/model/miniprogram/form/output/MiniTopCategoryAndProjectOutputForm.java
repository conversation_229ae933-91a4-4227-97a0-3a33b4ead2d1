package com.zhelian.model.miniprogram.form.output;


import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class MiniTopCategoryAndProjectOutputForm {

    @JSONField(name = "ID")
    public Integer ID;
    @<PERSON><PERSON><PERSON>ield(name = "Name")
    public String Name;
    @J<PERSON><PERSON>ield(name = "ParentID")
    public Integer ParentID;
    @JSONField(name = "IsProject")
    public Boolean IsProject;
    @JSONField(name = "Child")
    public List<MiniCategoryAndProjectOutputForm> Child;

}
