package com.zhelian.model.miniprogram.form.output;


import com.zhelian.model.base.BasePage;

public class MiniAppointmentBillQueryForm extends BasePage {

    public String Name;
    public String EmployeeID;
    public String AppointmentDate;
    public String Type;
    public String Status;
    public Integer CustomerID;
    public Integer AppointmentTypeID;
    public String CreatedBy;
    public String CreatedOnStartDate;
    public String CreatedOnEndDate;
    public Integer ServicerID;
    public String ServicerEmployeeID;
    public String Channel;
    public Integer CustomerSourceID;
}
