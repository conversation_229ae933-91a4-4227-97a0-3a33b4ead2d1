package com.zhelian.model.miniprogram.form.output;

import com.zhelian.model.CRM.form.output.ChannelDeveloperOutputForm;
import com.zhelian.model.ibeauty.form.output.AppointmentBillServicerInfoOutputForm;

import java.util.List;

public class MiniAppointmentBillOutputForm {

    public String ID;
    public Integer CustomerID;
    public String CustomerName;
    public String PhoneNumber;
    public String AppointmentDate;
    public String Period;
    public String Remark;
    public String Type;
    public String Status;
    public String EntityName;
    public String TimeOut;
    public String LevelName;
    public Integer AppointmentTypeID;
    public String AppointmentTypeName;
    public String CustomerSourceName;
    public String CreatedBy;
    public String CreatedOn;
    public Integer ChannelID;
    public String ChannelName;
    public List<AppointmentBillServicerInfoOutputForm> Servicer;
    public List<MiniAppointmentBillAppointmentProjectOutputForm> Project;
    public List<MiniAppointmentBillAppointmentAudioOutputForm> Audio;
    public List<ChannelDeveloperOutputForm> DeveloperList;
    public List<ChannelDeveloperOutputForm> ConsultantList;
}
