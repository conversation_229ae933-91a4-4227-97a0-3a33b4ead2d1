package com.zhelian.model.miniprogram.form.output;

import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;
import java.util.List;

public class ProjectDataOutputForm {

    @J<PERSON><PERSON><PERSON>(name = "ID")
    public Integer ID;
    @J<PERSON><PERSON><PERSON>(name = "ParentID")
    public Integer ParentID;
    @J<PERSON><PERSON>ield(name = "Name")
    public String Name;
    @J<PERSON><PERSON>ield(name = "SalePerformance")
    public BigDecimal SalePerformance;
    @J<PERSON><PERSON>ield(name = "SaleAmount")
    public BigDecimal SaleAmount;
    @J<PERSON><PERSON>ield(name = "ConsumePerformance")
    public BigDecimal ConsumePerformance;
    @J<PERSON><PERSON><PERSON>(name = "ConsumeAmount")
    public BigDecimal ConsumeAmount;
    @J<PERSON><PERSON><PERSON>(name = "ConsumePassengerFlow")
    public BigDecimal ConsumePassengerFlow;
    @J<PERSON><PERSON>ield(name = "ConsumeProjectAmount")
    public BigDecimal ConsumeProjectAmount;
    @<PERSON><PERSON><PERSON><PERSON>(name = "UnConsumeProjectAmount")
    public BigDecimal UnConsumeProjectAmount;
    @J<PERSON>NField(name = "UnConsumeMoney")
    public BigDecimal UnConsumeMoney;
    @J<PERSON><PERSON>ield(name = "Child")
    public List<ProjectChildDataOutputForm> Child;

}
