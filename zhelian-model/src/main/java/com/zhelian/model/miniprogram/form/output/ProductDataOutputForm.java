package com.zhelian.model.miniprogram.form.output;

import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;
import java.util.List;

public class ProductDataOutputForm {

    @J<PERSON><PERSON><PERSON>(name = "ID")
    public Integer ID;
    @J<PERSON><PERSON><PERSON>(name = "ParentID")
    public Integer ParentID;
    @J<PERSON><PERSON>ield(name = "Name")
    public String Name;
    @J<PERSON><PERSON>ield(name = "SalePerformance")
    public BigDecimal SalePerformance;
    @J<PERSON><PERSON>ield(name = "SaleAmount")
    public BigDecimal SaleAmount;
    @J<PERSON><PERSON>ield(name = "Balance")
    public BigDecimal Balance;
    @<PERSON><PERSON><PERSON><PERSON>(name = "BalanceMoney")
    public BigDecimal BalanceMoney;
    @J<PERSON><PERSON>ield(name = "Outbound")
    public BigDecimal Outbound;
    @J<PERSON><PERSON>ield(name = "Inbound")
    public BigDecimal Inbound;
    @J<PERSON>NField(name = "Stock")
    public BigDecimal Stock;
    @JSONField(name = "Child")
    public List<ProductChildDataOutputForm> Child;

}
