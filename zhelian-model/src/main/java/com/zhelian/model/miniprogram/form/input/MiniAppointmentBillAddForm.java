package com.zhelian.model.miniprogram.form.input;


import com.zhelian.model.ibeauty.form.input.AppointmentBillProjectAddForm;
import com.zhelian.model.ibeauty.form.input.AppointmentBillServicerAddForm;

import java.util.List;

public class MiniAppointmentBillAddForm {

    public Integer CustomerID;
    public String AppointmentDate;
    public Integer Period;
    public Integer AppointmentTypeID;
    public String Remark;
    public List<AppointmentBillServicerAddForm> Servicer;
    public List<AppointmentBillProjectAddForm> Project;
    public List<MiniAppointmentAudioForm> Audio;
}
