package com.zhelian.model.miniprogram.form.input;


import com.zhelian.model.ibeauty.form.input.AppointmentBillProjectAddForm;
import com.zhelian.model.ibeauty.form.input.AppointmentBillServicerAddForm;

import java.util.List;

public class MiniAppointmentBillUpdateForm {
    public String ID;

    public Integer CustomerID;
    public String AppointmentDate;
    public Integer Period;
    public Integer AppointmentTypeID;
    public String Remark;
    public String Status;
    public List<AppointmentBillServicerAddForm> Servicer;
    public List<AppointmentBillProjectAddForm> Project;
    public List<MiniAppointmentAudioForm> Audio;
}
