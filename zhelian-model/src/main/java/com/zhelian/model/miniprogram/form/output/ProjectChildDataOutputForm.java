package com.zhelian.model.miniprogram.form.output;

import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;

public class ProjectChildDataOutputForm {
    @J<PERSON>NField(name = "ID")
    public Integer ID;
    @J<PERSON><PERSON><PERSON>(name = "<PERSON>rentID")
    public Integer ParentID;
    @J<PERSON><PERSON><PERSON>(name = "Name")
    public String Name;
    @J<PERSON><PERSON>ield(name = "SalePerformance")
    public BigDecimal SalePerformance;
    @J<PERSON><PERSON>ield(name = "SaleAmount")
    public BigDecimal SaleAmount;
    @J<PERSON>NField(name = "ConsumePerformance")
    public BigDecimal ConsumePerformance;
    @J<PERSON><PERSON><PERSON>(name = "ConsumeAmount")
    public BigDecimal ConsumeAmount;
    @J<PERSON><PERSON><PERSON>(name = "ConsumePassengerFlow")
    public BigDecimal ConsumePassengerFlow;
    @JSONField(name = "ConsumeProjectAmount")
    public BigDecimal ConsumeProjectAmount;
    @JSONField(name = "UnConsumeProjectAmount")
    public BigDecimal UnConsumeProjectAmount;
    @J<PERSON><PERSON><PERSON>(name = "UnConsumeMoney")
    public BigDecimal UnConsumeMoney;
}
