package com.zhelian.model.miniprogram.form.output;

import java.math.BigDecimal;

public class EntityAllPerformanceOutputForm {

    public BigDecimal PayAmount;
    public BigDecimal CardDeductionAmount;
    public BigDecimal SalePerformanceTotal;
    public BigDecimal SalePerformance;
    public BigDecimal SalePayPerformance;
    public BigDecimal SaleCardDeductionPerformance;
    public BigDecimal SaleCardDeductionLargessPerformance;
    public BigDecimal RefundSalePerformance;
    public BigDecimal RefundSalePayPerformance;
    public BigDecimal RefundSaleCardDeductionPerformance;
    public BigDecimal RefundSaleCardDeductionLargessPerformance;
    public BigDecimal TreatPerformanceTotal;
    public BigDecimal TreatPerformance;
    public BigDecimal TreatPrincipal;
    public BigDecimal TreatCardPrincipal;
    public BigDecimal TreatCardLargessPrincipal;
    public BigDecimal TreatLargessPrincipal;
    public BigDecimal RefundTreatPerformance;
    public BigDecimal RefundTreatPrincipal;
    public BigDecimal RefundTreatCardPrincipal;
    public BigDecimal RefundTreatCardLargessPrincipal;
    public BigDecimal RefundTreatLargessPrincipal;

}
