package com.zhelian.model.miniprogram.form.output;


import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;

public class SavingCardChildDataOutputForm {
    @J<PERSON><PERSON>ield(name = "ID")
    public Integer ID;
    @<PERSON><PERSON><PERSON><PERSON>(name = "ParentID")
    public Integer ParentID;
    @J<PERSON><PERSON>ield(name = "Name")
    public String Name;
    @J<PERSON><PERSON>ield(name = "Amount")
    public BigDecimal Amount;
    @J<PERSON><PERSON>ield(name = "LargessAmount")
    public BigDecimal LargessAmount;
    @JSONField(name = "AmountUsed")
    public BigDecimal AmountUsed;
    @J<PERSON><PERSON>ield(name = "LargessAmountUsed")
    public BigDecimal LargessAmountUsed;
    @J<PERSON><PERSON>ield(name = "AmountBalance")
    public BigDecimal AmountBalance;
    @JSONField(name = "LargessAmountBalance")
    public BigDecimal LargessAmountBalance;
}
