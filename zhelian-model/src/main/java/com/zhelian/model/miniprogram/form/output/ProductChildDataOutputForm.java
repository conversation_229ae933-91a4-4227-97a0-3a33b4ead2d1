package com.zhelian.model.miniprogram.form.output;

import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;

public class ProductChildDataOutputForm {
    @J<PERSON><PERSON>ield(name = "ID")
    public Integer ID;
    @<PERSON><PERSON><PERSON><PERSON>(name = "ParentID")
    public Integer ParentID;
    @J<PERSON><PERSON><PERSON>(name = "Name")
    public String Name;
    @J<PERSON><PERSON>ield(name = "SalePerformance")
    public BigDecimal SalePerformance;
    @J<PERSON><PERSON>ield(name = "SaleAmount")
    public BigDecimal SaleAmount;
    @<PERSON><PERSON><PERSON>ield(name = "Balance")
    public BigDecimal Balance;
    @J<PERSON><PERSON>ield(name = "BalanceMoney")
    public BigDecimal BalanceMoney;
    @J<PERSON><PERSON>ield(name = "Outbound")
    public BigDecimal Outbound;
    @J<PERSON><PERSON>ield(name = "Inbound")
    public BigDecimal Inbound;
    @J<PERSON><PERSON>ield(name = "Stock")
    public BigDecimal Stock;
}
