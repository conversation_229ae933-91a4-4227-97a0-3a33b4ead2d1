/**
 * <AUTHOR>
 * @date 2025-01-15
 * @description 坐席点击呼叫表单类
 * @version 1.0
 */
package com.zhelian.model.ibeauty.form.input;

public class ClickCallForm {

    /**
     * 坐席工号（必填）
     */
    public String agent;

    /**
     * 被叫号码（必填）
     */
    public String callee;

    /**
     * 主叫号码（非必填）
     */
    public String caller;

    /**
     * 是否使用电话管家（非必填）
     * 1使用0不使用默认0
     */
    public String localCall;

    /**
     * 电话管家呼叫方式（非必填）
     * 1标识使用小号资源0标识系统自主决策默认0
     */
    public String localCallMode;

    /**
     * 是否使用远端黑名单（非必填）
     * 1-不使用远端黑名单0-使用远端黑名单默认0
     */
    public String noUseBlApi;

    /**
     * 呼叫模式（非必填）
     * 1-SIP中继2-小号模式3-回拨模式4-本地直拨5-SIP小号默认0
     */
    public String lineMode;

    /**
     * 对接终端（非必填）
     * carlPhone 卡尔话机，默认为空
     */
    public String apiTerminal;

    /**
     * 来源标识（非必填）
     * 同一来源下的标识不允许重复，在企业下【业务】-【主叫设定】里配置
     */
    public String identifier;

    /**
     * 用户回传数据（非必填）
     */
    public String userData;
}
