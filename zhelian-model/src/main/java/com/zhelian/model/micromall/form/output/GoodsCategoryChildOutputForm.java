package com.zhelian.model.micromall.form.output;

import com.alibaba.fastjson.annotation.JSONField;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderResult;

import java.util.List;

public class GoodsCategoryChildOutputForm {


    @JSONField(name = "ID")
    public Integer ID;

    @JSONField(name = "Name")
    public String Name;

    @JSONField(name = "ParentID")
    public Integer ParentID;

    @JSONField(name = "Goods")
    public List<GoodsAllOutputForm> Goods;

    @JSONField(name = "wxPayUnifiedOrderResult")
    public WxPayUnifiedOrderResult wxPayUnifiedOrderResult;
}
