package com.zhelian.model.micromall.form.output;


import java.math.BigDecimal;
import java.util.List;

public class GrouponBillDetailOutForm {

    public String BillDate;
    public String GrouponName;
    public Integer Quota;
    public String ShareText;
    public Integer Quantity;
    public Integer Balance;
    public Integer GrouponID;
    public Integer GrouponPriceID;
    public Integer PeopleNum;
    public Integer GroupTimeOutDay;
    public Integer GroupTimeOutHour;
    public Integer GroupTimeOutMinuter;
    public Integer GoodsID;
    public String GoodsType;
    public String GoodsName;
    public BigDecimal Price;
    public BigDecimal GroupPrice;
    public String GoodsImageUrl;
    public Boolean IsGroupon;
    public List<GrouponBillCustomerOutForm> Customer;

}
