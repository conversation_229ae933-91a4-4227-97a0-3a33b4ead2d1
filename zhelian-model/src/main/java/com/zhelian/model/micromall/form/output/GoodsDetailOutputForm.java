package com.zhelian.model.micromall.form.output;


import java.io.File;
import java.math.BigDecimal;
import java.util.List;

public class GoodsDetailOutputForm {

    public Integer ID;
    public String Name;
    public String GoodsTypeName;
    public BigDecimal Price;
    public String ValidDay;
    public String Amount;
    public String ConsumeCycle;
    public String CycleLimitAmount;
    public String OriginalText;
    public String Memo;
    public String Specification;
    public String UnitName;
    public Integer ConsumeNum;
    public Integer ApplyNum;
    public Integer SavingCardNum;
    public Integer SavingCardLargessNum;
    public Integer PackageGoodsNum;
    public Integer PackageLargessGoodsNum;
    public List<GoodsDetailImgUrlOutputForm> ImgUrlList;
    public Boolean IsSettingLargess;
    public Boolean IsGoodsRange;
    public Boolean IsLargessGoodsRange;
    public File wxaCodeUnlimit;

}
