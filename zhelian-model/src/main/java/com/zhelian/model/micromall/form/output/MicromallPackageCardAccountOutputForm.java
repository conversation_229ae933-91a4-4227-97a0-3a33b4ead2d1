package com.zhelian.model.micromall.form.output;

import java.math.BigDecimal;
import java.util.List;

public class MicromallPackageCardAccountOutputForm {

    public Integer AccountID;
    public String Name;
    public String Alias;
    public String ValidDayName;
    public String BuyDate;
    public BigDecimal Price;
    public BigDecimal ArrearAmount;
    public BigDecimal TotalAmount;
    public String ChannelName;
    public Integer Count;
    public Integer ValidCount;
    public Boolean IsLargess;

    public List<MicromallProductAccountOutputForm> Product;
    public List<MicromallProjectAccountOutputForm> Project;
    public List<MicromallGeneralCardAccountOutputForm> GeneralCard;
    public List<MicromallTimeCardAccountOutputForm> TimeCard;
    public List<MicromallSavingCardAccountOutputForm> SavingCard;
}
