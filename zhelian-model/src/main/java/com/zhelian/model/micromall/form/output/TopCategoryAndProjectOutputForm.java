package com.zhelian.model.micromall.form.output;


import com.alibaba.fastjson.annotation.JSONField;
import com.zhelian.model.ibeauty.form.output.CategoryAndProjectOutputForm;

import java.util.List;

public class TopCategoryAndProjectOutputForm {

    @JSONField(name = "ID")
    public Integer ID;
    @JSONField(name = "Name")
    public String Name;
    @J<PERSON><PERSON>ield(name = "ParentID")
    public Integer ParentID;
    @JSONField(name = "IsProject")
    public Boolean IsProject;
    @JSONField(name = "Child")
    public List<CategoryAndProjectOutputForm> Child;

}
