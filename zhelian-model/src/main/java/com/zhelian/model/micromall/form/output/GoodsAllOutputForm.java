package com.zhelian.model.micromall.form.output;


import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;

public class GoodsAllOutputForm {
    @JSONField(name = "GoodsType")
    public String GoodsType;
    @J<PERSON><PERSON>ield(name = "GoodsTypeName")
    public String GoodsTypeName;
    @JSONField(name = "GoodsID")
    public Integer GoodsID;
    @JSONField(name = "GoodsName")
    public String GoodsName;
    @JSONField(name = "Price")
    public BigDecimal Price;
    @JSONField(name = "OriginalText")
    public String OriginalText;
    @J<PERSON>NField(name = "ImageURL")
    public String ImageURL;
}
