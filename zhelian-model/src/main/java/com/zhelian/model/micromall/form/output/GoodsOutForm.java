package com.zhelian.model.micromall.form.output;

import java.math.BigDecimal;
import java.util.List;

public class GoodsOutForm {


    //商品ID
    public Integer GoodsID;

    public Integer OfflineGoodsID;

    //商品类型
    public String GoodsTypeID;

    public String GoodsTypeName;

    //线上商品名称
    public String OnlineName;

    //线下商品名称
    public String OfflineName;

    //价格
    public BigDecimal Price;

    //分类ID
    public Integer CategoryID;
    public String CategoryName;

    //简介
    public String Description;

    //详情
    public String Detail;

    //是否上架
    public Boolean Active;

    //线上销售数量
    public Integer Quantity;

    //排序
    public Integer Sequence;


    public List<GoodsImageOutForm> Image;
    public List<Integer> Entitys;
}
