package com.zhelian.model.micromall.form.output;

import com.zhelian.model.khs.form.output.EntityBusinessDateOutputForm;
import com.zhelian.model.khs.form.output.EntityPhotoUrlOutputForm;

import java.math.BigDecimal;
import java.util.List;

public class EntityListOutForm {

    public Integer ID;
    public String EntityName;
    public String ParentID;
    public String AddressDetail;
    public String ServiceTelephoneNumber;
    public String BusinessStartTime;
    public String BusinessEndTime;
    public String EntityLogoURL;
    public String Description;
    public BigDecimal Longitude;
    public BigDecimal Latitude;
    public Integer Distance;
    public List<EntityBusinessDateOutputForm> EntityBusinessDate;
    public List<EntityPhotoUrlOutputForm> PhotoURLList;
}
