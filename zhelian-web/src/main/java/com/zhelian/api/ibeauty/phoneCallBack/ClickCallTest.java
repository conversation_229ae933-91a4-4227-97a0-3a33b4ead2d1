package com.zhelian.api.ibeauty.phoneCallBack;

import com.zhelian.model.ibeauty.form.input.ClickCallForm;

public class ClickCallTest {

    public static void main(String[] args) {
        ClickCallForm form = new ClickCallForm();

        // Required parameters
        form.agent = "1001";
        form.callee = "13100000000";

        // Optional parameters
        form.caller = "9500123";
        form.userData = "test123";
        form.lineMode = "0";
        form.noUseBlApi = "0";
        form.localCall = "0";
        form.localCallMode = "0";
        form.identifier = "callertest1";

        System.out.println("Click Call Parameters:");
        System.out.println("Agent: " + form.agent);
        System.out.println("Callee: " + form.callee);
        System.out.println("Caller: " + form.caller);
        System.out.println("UserData: " + form.userData);
        System.out.println("LineMode: " + form.lineMode);
        System.out.println("Identifier: " + form.identifier);

        System.out.println("\nAPI Endpoint: /api/phoneCallBack/clickCall");
        System.out.println("Method: POST");
        System.out.println("Content-Type: application/json");

        System.out.println("\nRequest Body Example:");
        System.out.println("{");
        System.out.println("  \"agent\": \"" + form.agent + "\",");
        System.out.println("  \"callee\": \"" + form.callee + "\",");
        System.out.println("  \"caller\": \"" + form.caller + "\",");
        System.out.println("  \"userData\": \"" + form.userData + "\",");
        System.out.println("  \"lineMode\": \"" + form.lineMode + "\",");
        System.out.println("  \"identifier\": \"" + form.identifier + "\"");
        System.out.println("}");
    }
}
