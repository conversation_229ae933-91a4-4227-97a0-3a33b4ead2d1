package com.zhelian.api.ibeauty.phoneCallBack;

import com.zhelian.model.ibeauty.form.input.ClickCallForm;

public class ClickCallTest {

    public static void main(String[] args) {
        ClickCallForm form = new ClickCallForm();

        // Required parameters only
        form.agent = "1001";
        form.callee = "13100000000";

        System.out.println("Click Call Parameters:");
        System.out.println("Agent: " + form.agent);
        System.out.println("Callee: " + form.callee);

        System.out.println("\nAPI Endpoint: /api/phoneCallBack/clickCall");
        System.out.println("Method: POST");
        System.out.println("Content-Type: application/json");

        System.out.println("\nRequest Body Example:");
        System.out.println("{");
        System.out.println("  \"agent\": \"" + form.agent + "\",");
        System.out.println("  \"callee\": \"" + form.callee + "\"");
        System.out.println("}");

        System.out.println("\nConfiguration Info:");
        System.out.println("Customer: C27");
        System.out.println("Version: 2.0.6");
        System.out.println("Final URL: http://cc.crm666.com/openapi/V2.0.6/callNumber");
    }
}
