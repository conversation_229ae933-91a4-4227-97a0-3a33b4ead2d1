package com.zhelian.api.ibeauty.phoneCallBack;

import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.ibeauty.phoneCallBack.PhoneCallBackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/api")
public class PhoneCallBackController {

    @Autowired
    PhoneCallBackService phoneCallBackService;

    @RequestMapping(value = "/phoneCallBack/list", method = {RequestMethod.POST})
    public BaseOutput list(@RequestBody PhoneCallBackQueryForm form) {
        return phoneCallBackService.list(form);
    }

    @RequestMapping(value = "/phoneCallBack/add", method = {RequestMethod.POST})
    public BaseOutput add(@RequestBody PhoneCallBackAddForm form) throws Exception {
        if (!StringUtils.hasText(form.EmployeeID)){
            return BaseOutput.failed("请选择员工");
        }
        if (!StringUtils.hasText(form.PhoneNumber)){
            return BaseOutput.failed("请填写手机号");
        }
        if (!StringUtils.hasText(form.EcpId)) {
            return BaseOutput.failed("请填写能力号");
        }
        return phoneCallBackService.addDx(form);
    }

    @RequestMapping(value = "/phoneCallBack/delete", method = {RequestMethod.POST})
    public BaseOutput delete(@RequestBody PhoneCallBackDeleteForm form) {
        if (form.ID == null){
            return BaseOutput.failed("缺少编号");
        }
        return phoneCallBackService.delete(form);
    }

    @RequestMapping(value = "/phoneCallBack/callBack", method = {RequestMethod.POST})
    public BaseOutput callBack(@RequestBody PhoneCallBackForm form) {
        if (form.CustomerID == null){
            return BaseOutput.failed("请选择顾客");
        }
        return phoneCallBackService.callBackDx(form);
    }

    @RequestMapping(value = "/phoneCallBack/callBackAccept", method = {RequestMethod.POST})
    public BaseOutput callBackAccept(@RequestBody PhoneCallBackAcceptForm form) throws Exception {
        return phoneCallBackService.callBackAccept(form);
    }

    @RequestMapping(value = "/phoneCallBack/callBackLog", method = {RequestMethod.POST})
    public BaseOutput callBackLog(@RequestBody PhoneCallBackLogForm form){
        if (form.CustomerID == null){
            return BaseOutput.failed("请选择顾客");
        }
        return phoneCallBackService.callBackLog(form);
    }

    @RequestMapping(value = "/phoneCallBack/callBackLogReport")
    public BaseOutput callBackLogReport(PhoneCallBackLogReportForm form) {
        return phoneCallBackService.callBackLogReport(form);
    }
    @RequestMapping(value = "/phoneCallBack/callBackLogReport/export")
    public void callBackLogReportExport(PhoneCallBackLogReportForm form, HttpServletResponse response) {
        phoneCallBackService.callBackLogReportExport(form, response);
    }

    @RequestMapping(value = "/phoneCallBack/callBackLogStatisticsReport")
    public BaseOutput callBackLogStatisticsReport(PhoneCallBackLogStatisticsForm form){
        return phoneCallBackService.callBackLogStatisticsReport(form);
    }

    @RequestMapping(value = "/phoneCallBack/callBackLogStatisticsReportTotal")
    public BaseOutput callBackLogStatisticsReportTotal(PhoneCallBackLogStatisticsForm form){
        return phoneCallBackService.callBackLogStatisticsReportTotal(form);
    }

    @RequestMapping(value = "/phoneCallBack/callBackLogStatisticsReport/export")
    public void callBackLogStatisticsReportExport(PhoneCallBackLogStatisticsForm form, HttpServletResponse response){
        phoneCallBackService.callBackLogStatisticsReportExport(form, response);
    }

    /**
     * 坐席点击呼叫
     * @param form 坐席点击呼叫表单
     * @return 调用结果
     */
    @RequestMapping(value = "/phoneCallBack/clickCall", method = {RequestMethod.POST})
    public BaseOutput clickCall(@RequestBody ClickCallForm form) {
        if (!StringUtils.hasText(form.agent)) {
            return BaseOutput.failed("请填写坐席工号");
        }
        if (!StringUtils.hasText(form.callee)) {
            return BaseOutput.failed("请填写被叫号码");
        }
        return phoneCallBackService.clickCall(form);
    }
}
