package util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

public class PhoneCallBackUtil {

    public static final String appkey ="";
    /**
     * 电信appKey
     */
    public static final String DX_APP_KEY ="";
    public static final String yw_code ="";
    public static final String Url ="";
    /**
     * 电信回拨
     */
    public static final String DX_URL ="";
    public static final String addSeatUrl ="";
    public static final String recordUrl ="";
    public static final String DX_RECORD_URL ="";
    /**
     * 坐席点击呼叫配置信息
     * 格式：customer@password@version
     */
    public static final String CLICK_CALL_CONFIG = "C27@2113B296C2C16F5A6258E4BE41061712BB6DAD64@2.0.6";

    /**
     * 坐席点击呼叫接口地址
     */
    public static final String CLICK_CALL_URL = "http://cc.crm666.com/openapi/V{version}/callNumber";

    public static void main(String[] args) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appkey",appkey);
        jsonObject.put("yw_code",PhoneCallBackUtil.yw_code);
        jsonObject.put("phone","18778053671");
        JSONObject jsonObject1 = PhoneCallBackUtil.sendHttpPost(JSON.toJSONString(jsonObject), PhoneCallBackUtil.addSeatUrl);
        System.out.println(jsonObject1.toString());
    }

    public static JSONObject sendHttpPost(String param,String url) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
        StringEntity requestentity = new StringEntity(param, "UTF-8");
        requestentity.setContentType("application/json;charset=UTF-8");
        httpPost.setEntity(requestentity);
        CloseableHttpResponse response = httpClient.execute(httpPost);
        HttpEntity entity = response.getEntity();
        String responseContent = EntityUtils.toString(entity, "UTF-8");
        JSONObject jsonObject = JSONObject.parseObject(responseContent);
        response.close();
        httpClient.close();

        return jsonObject;
    }
}
