package com.zhelian.service.ibeauty.clueDistribution;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.util.StringUtil;
import com.zhelian.core.autoNumber.AutoNumberUtils;
import com.zhelian.mapper.CRM.CustomerMapper;
import com.zhelian.mapper.CRM.FollowUpMapper;
import com.zhelian.mapper.ibeauty.ClueDistributionMapper;
import com.zhelian.model.CRM.entity.FollowUpRecordEntity;
import com.zhelian.model.CRM.enums.LeadSourceEnum;
import com.zhelian.model.base.BaseOutput;
import com.zhelian.model.base.BaseOutputForm;
import com.zhelian.model.base.BasePageInfo;
import com.zhelian.model.ibeauty.entity.*;
import com.zhelian.model.ibeauty.form.input.*;
import com.zhelian.service.weixin.WeiXinMessageService;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.URI;
import java.security.MessageDigest;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class ClueDistributionService {

    @Autowired
    private ClueDistributionMapper clueDistributionMapper;

    @Autowired
    CustomerMapper customerMapper;

    @Autowired
    FollowUpMapper followUpMapper;

    private static final Logger logger = LoggerFactory.getLogger(ClueDistributionService.class);

    public BaseOutput employeeConfigAll() {
        return BaseOutputForm.success(clueDistributionMapper.employeeConfigAll());
    }

    public BaseOutput addEmployeeConfig(ClueDistributionEmployeeConfigAddForm form) {
        clueDistributionMapper.deleteEmployeeConfig(form.EmployeeID);
        clueDistributionMapper.addEmployeeConfig(form.EmployeeID);
        return BaseOutput.success();
    }


    public BaseOutput list(ClueDistributionQueryForm form) {
        PageHelper.startPage(form.PageNum, form.getPageSize());
        return BasePageInfo.success(clueDistributionMapper.list(form.ProvinceCode, form.CityCode, form.Active));
    }

    public BaseOutput detail(ClueDistributionDetailQueryForm form) {
        return BaseOutputForm.success(clueDistributionMapper.detail(form.ID));
    }

    @Transactional
    public BaseOutput add(ClueDistributionAddForm form) {

        //判断是否存在线索区域
        ClueDistributionEntity clueDistributionEntity = clueDistributionMapper.getClueDistributionByProvinceCodeAndCityCode(form.ProvinceCode, form.CityCode);
        if (clueDistributionEntity != null) {
            throw new RuntimeException("区域已存在");
        }

        //保存线索区域
        ClueDistributionEntity entity = new ClueDistributionEntity();
        entity.ProvinceCode = form.ProvinceCode;
        entity.CityCode = form.CityCode;
        entity.Active = true;
        clueDistributionMapper.addClueDistribution(entity);

        //保存线索区域负责员工
        List<ClueDistributionEmployeeEntity> employeeEntityList = new ArrayList<>();
        form.Detail.forEach(detail -> {
            ClueDistributionEmployeeEntity clueDistributionEmployeeEntity = new ClueDistributionEmployeeEntity();
            clueDistributionEmployeeEntity.ID = entity.ID;
            clueDistributionEmployeeEntity.EmployeeID = detail.EmployeeID;
            clueDistributionEmployeeEntity.DistributionNum = 0;
            employeeEntityList.add(clueDistributionEmployeeEntity);
        });
        clueDistributionMapper.addClueDistributionEmployee(employeeEntityList);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput update(ClueDistributionUpdateForm form) {

        ClueDistributionEntity clueDistributionEntity = clueDistributionMapper.getClueDistributionByID(form.ID);

        if (clueDistributionEntity == null) {
            throw new RuntimeException("区域不存在");
        }

        //更新线索区域有效性
        ClueDistributionEntity entity = new ClueDistributionEntity();
        entity.ID = form.ID;
        entity.Active = form.Active;
        clueDistributionMapper.updateClueDistribution(entity);

        //保存线索区域负责员工
        clueDistributionMapper.deleteClueDistributionEmployeeByID(entity.ID);
        List<ClueDistributionEmployeeEntity> employeeEntityList = new ArrayList<>();
        form.Detail.forEach(detail -> {
            ClueDistributionEmployeeEntity clueDistributionEmployeeEntity = new ClueDistributionEmployeeEntity();
            clueDistributionEmployeeEntity.ID = entity.ID;
            clueDistributionEmployeeEntity.EmployeeID = detail.EmployeeID;
            clueDistributionEmployeeEntity.DistributionNum = 0;
            employeeEntityList.add(clueDistributionEmployeeEntity);
        });
        clueDistributionMapper.addClueDistributionEmployee(employeeEntityList);

        return BaseOutput.success();
    }

    @Transactional
    public BaseOutput authCode(String state, String auth_code) {
        // 请求地址
        String open_api_url_prefix = "https://ad.oceanengine.com/open_api/";
        String uri = "oauth2/access_token/";
        List<ClueDistributionConfigEntity> clueDistributionConfigEntity = clueDistributionMapper.getClueDistributionConfigEntity();

        for (ClueDistributionConfigEntity entity : clueDistributionConfigEntity) {
            // 请求参数
            Map<String, Object> data = new HashMap<>();
            data.put("app_id", entity.AppID);
            data.put("secret", entity.Secret);
            data.put("grant_type", "auth_code");
            data.put("auth_code", auth_code);

            // 构造请求
            HttpPost httpEntity = new HttpPost(open_api_url_prefix + uri);

            CloseableHttpResponse response = null;
            CloseableHttpClient client = null;

            try {
                client = HttpClientBuilder.create().build();
                httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(data), ContentType.APPLICATION_JSON));

                response = client.execute(httpEntity);
                if (response != null && response.getStatusLine().getStatusCode() == 200) {
                    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
                    StringBuffer result = new StringBuffer();
                    String line = "";
                    while ((line = bufferedReader.readLine()) != null) {
                        result.append(line);
                    }
                    bufferedReader.close();
                    JSONObject jsonObject1 = JSONObject.parseObject(result.toString());
                    if (jsonObject1 != null) {
                        JSONObject data1 = (JSONObject) jsonObject1.get("data");
                        if (data1 != null && data1.size() > 0) {
                            entity.State = state;
                            entity.AuthCode = auth_code;
                            entity.AccessToken = data1.getString("access_token");
                            entity.RefreshToken = data1.getString("refresh_token");
                            clueDistributionMapper.updateClueDistributionConfigEntity(entity);
                        }
                    }
                }

            } catch (ClientProtocolException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (response != null) {
                        response.close();
                    }
                    client.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return BaseOutput.success();
    }

    @Transactional
    public void refreshAccessToken() {

        // 请求地址
        String open_api_url_prefix = "https://ad.oceanengine.com/open_api/";
        String uri = "oauth2/refresh_token/";

        List<ClueDistributionConfigEntity> tokenList = clueDistributionMapper.getClueDistributionConfigEntity();

        for (ClueDistributionConfigEntity entity : tokenList) {
            // 请求参数1
            Map<String, Object> data = new HashMap();
            data.put("appid", entity.AppID);
            data.put("app_id", entity.AppID);
            data.put("secret", entity.Secret);
//            data.put("grant_type", "refresh_token");
            data.put("refresh_token", entity.RefreshToken);

            // 构造请求
            HttpPost httpEntity = new HttpPost(open_api_url_prefix + uri);

            CloseableHttpResponse response = null;
            CloseableHttpClient client = null;

            try {
                client = HttpClientBuilder.create().build();
                httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(data), ContentType.APPLICATION_JSON));

                response = client.execute(httpEntity);
                if (response != null && response.getStatusLine().getStatusCode() == 200) {
                    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
                    StringBuffer result = new StringBuffer();
                    String line = "";
                    while ((line = bufferedReader.readLine()) != null) {
                        result.append(line);
                    }
                    bufferedReader.close();
                    JSONObject jsonObject1 = JSONObject.parseObject(result.toString());
                    JSONObject data1 = (JSONObject) jsonObject1.get("data");
                    logger.error("每十分钟刷新token：{}", jsonObject1);
                    if (data1 != null){
                        entity.AccessToken = StringUtil.isEmpty(data1.getString("access_token")) ? null : data1.getString("access_token");
                        entity.RefreshToken = StringUtil.isEmpty(data1.getString("refresh_token")) ? null : data1.getString("refresh_token");
                        if (!StringUtil.isEmpty(entity.AccessToken)) {
                            clueDistributionMapper.updateClueDistributionConfigEntity(entity);
                            getAdvertiser(entity);
                        }
                    }
                }

            } catch (ClientProtocolException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (response != null) {
                        response.close();
                    }
                    client.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void getAdvertiser(ClueDistributionConfigEntity entity) {
        String access_token = entity.AccessToken;
        // 请求地址
        String open_api_url_prefix = "https://ad.oceanengine.com/open_api/";
        String uri = "oauth2/advertiser/get/";

        // 请求参数
        Map data = new HashMap();
        data.put("access_token", access_token);

        // 构造请求
        HttpEntityEnclosingRequestBase httpEntity = new HttpEntityEnclosingRequestBase() {
            @Override
            public String getMethod() {
                return "GET";
            }
        };

        httpEntity.setHeader("access_token", access_token);

        CloseableHttpResponse response = null;
        CloseableHttpClient client = null;

        try {
            client = HttpClientBuilder.create().build();
            httpEntity.setURI(URI.create(open_api_url_prefix + uri));
            httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(data), ContentType.APPLICATION_JSON));

            response = client.execute(httpEntity);
            if (response != null && response.getStatusLine().getStatusCode() == 200) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
                StringBuffer result = new StringBuffer();
                String line = "";
                while ((line = bufferedReader.readLine()) != null) {
                    result.append(line);
                }
                bufferedReader.close();
                JSONObject jsonObject1 = JSONObject.parseObject(result.toString());
                if (jsonObject1 != null){
                    JSONObject data1 = (JSONObject) jsonObject1.get("data");
                    if (data1 != null){
                        JSONArray list1 = (JSONArray) data1.get("list");
                        if (list1 != null) {
                            for (int i = 0; i < list1.size(); i++) {
                                JSONObject jsonObject = (JSONObject) list1.get(i);
                                getAdvertiser(entity, jsonObject.getString("advertiser_id"));
                            }
                        }
                    }
                }
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Transactional
    public void getAdvertiser(ClueDistributionConfigEntity entity, String advertiserId) {
        String access_token = entity.AccessToken;
        long advertiser_id = Long.parseLong(advertiserId);

        // 请求地址
        String open_api_url_prefix = "https://ad.oceanengine.com/open_api/2/";
        String uri = "majordomo/advertiser/select/";

        // 请求参数
        Map data = new HashMap();
        data.put("advertiser_id", advertiser_id);

        // 构造请求
        HttpEntityEnclosingRequestBase httpEntity = new HttpEntityEnclosingRequestBase() {
            @Override
            public String getMethod() {
                return "GET";
            }
        };

        httpEntity.setHeader("Access-Token", access_token);

        CloseableHttpResponse response = null;
        CloseableHttpClient client = null;

        try {
            client = HttpClientBuilder.create().build();
            httpEntity.setURI(URI.create(open_api_url_prefix + uri));
            httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(data), ContentType.APPLICATION_JSON));

            response = client.execute(httpEntity);
            if (response != null && response.getStatusLine().getStatusCode() == 200) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
                StringBuffer result = new StringBuffer();
                String line = "";
                while ((line = bufferedReader.readLine()) != null) {
                    result.append(line);
                }
                bufferedReader.close();
                JSONObject jsonObject1 = JSONObject.parseObject(result.toString());
                if (jsonObject1 != null){
                    JSONObject data1 = (JSONObject) jsonObject1.get("data");
                    if (data1 != null){
                        JSONArray list1 = (JSONArray) data1.get("list");
                        if (list1 != null && list1.size() > 0) {
                            String[] advertiser_ids = new String[list1.size()];
                            for (int i = 0; i < list1.size(); i++) {
                                JSONObject jsonObject = (JSONObject) list1.get(i);
                                advertiser_ids[i] = jsonObject.getString("advertiser_id");
                            }
                            getClueList(advertiser_ids, access_token, "1", entity.AppID);
                        }
                    }
                }

            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    @Transactional
    public void getClueList(String[] advertiser_ids, String access_token, String page, String appID) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -5); // 往前推5天
        Date d = cal.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat h = new SimpleDateFormat("HH");
        String format = h.format(new Date());
        String start_time = "";
        String end_time = "";
        if (format.equals("00")) {
            start_time = sdf.format(d);
            end_time = sdf.format(new Date());
        } else {
            start_time = sdf.format(d); // 使用往前推5天的时间作为start_time
            end_time = sdf.format(new Date());
        }
        // 请求地址
        String open_api_url_prefix = "https://ad.oceanengine.com/open_api/2/";
        String uri = "tools/clue/get/";

        // 请求参数
        Map<String, Object> data = new HashMap();
        data.put("advertiser_ids", advertiser_ids);
        data.put("start_time", start_time);
        data.put("end_time", end_time);
        data.put("page_size", 100);
        data.put("page", page);

        // 构造请求
        HttpEntityEnclosingRequestBase httpEntity = new HttpEntityEnclosingRequestBase() {
            @Override
            public String getMethod() {
                return "GET";
            }
        };

        httpEntity.setHeader("Access-Token", access_token);

        CloseableHttpResponse response = null;
        CloseableHttpClient client = null;

        try {
            client = HttpClientBuilder.create().build();
            httpEntity.setURI(URI.create(open_api_url_prefix + uri));
            httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(data), ContentType.APPLICATION_JSON));

            response = client.execute(httpEntity);
            if (response != null & response.getStatusLine().getStatusCode() == 200) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
                StringBuffer result = new StringBuffer();
                String line = "";
                while ((line = bufferedReader.readLine()) != null) {
                    result.append(line);
                }
                bufferedReader.close();

                JSONObject jsonObject1 = JSONObject.parseObject(result.toString());
                JSONObject data1 = jsonObject1.getJSONObject("data");
                if (data1 != null) {
                    JSONArray list = data1.getJSONArray("list");
                    if (list != null) {
                        save(list, appID);
                        JSONObject page_info = data1.getJSONObject("page_info");
                        if (page_info != null) {
                            Integer total_page = page_info.getInteger("total_page");
                            Integer pageS = page_info.getInteger("page");
                            for (int i = pageS; i < total_page; ) {
                                i = i + 1;
                                getClueList(advertiser_ids, access_token, String.valueOf(i), appID);
                            }
                        }
                    }
                }
            }

        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Transactional
    public synchronized void save(JSONArray data, String appID) {

        if (data != null) {
            for (int i = 0; i < data.size(); i++) {
                Object arrayElement = data.get(i);
                JSONObject jsonObject = new JSONObject((Map<String, Object>) arrayElement);
                ClueDistributionLogEntity clueDistributionLog = clueDistributionMapper.getClueDistributionLogByClueID(jsonObject.getString("clue_id"));

                if (clueDistributionLog == null) {
                    ClueDistributionLogEntity clueDistributionLogEntity = new ClueDistributionLogEntity();
                    clueDistributionLogEntity.ClueID = jsonObject.getString("clue_id");
                    clueDistributionLogEntity.AppID = appID;
                    clueDistributionLogEntity.Name = jsonObject.getString("name");
                    clueDistributionLogEntity.PhoneNumber = jsonObject.getString("telephone");
                    clueDistributionLogEntity.Gender = jsonObject.getString("gender");
                    clueDistributionLogEntity.County = jsonObject.getString("country_name");
                    String location = jsonObject.getString("location");
                    if (StringUtils.hasText(location)) {
                        String[] split = location.split("\\+");
                        clueDistributionLogEntity.Province = split[0];
                        if (split.length > 1) {
                            clueDistributionLogEntity.City = split[1];
                        }
                    }
                    clueDistributionLogEntity.Address = jsonObject.getString("address");
                    clueDistributionLogEntity.Remark = jsonObject.getString("remark");
                    clueDistributionLogEntity.CreateTime = new Date(Timestamp.valueOf(jsonObject.getString("create_time_detail")).getTime());
                    if (StringUtil.isEmpty(clueDistributionLogEntity.Name)) {
                        clueDistributionLogEntity.Log = "失败：姓名为空";
                        clueDistributionLogEntity.Success = false;
                    } else if (StringUtil.isEmpty(clueDistributionLogEntity.Gender)) {
                        clueDistributionLogEntity.Log = "失败：性别为空";
                        clueDistributionLogEntity.Success = false;
                    } else if (StringUtil.isEmpty(clueDistributionLogEntity.PhoneNumber)) {
                        clueDistributionLogEntity.Log = "失败：手机号为空";
                        clueDistributionLogEntity.Success = false;
                    } else if (clueDistributionLogEntity.CreateTime == null) {
                        clueDistributionLogEntity.Log = "失败：创建时间为空";
                        clueDistributionLogEntity.Success = false;
                    }

                    CustomerEntity customer = customerMapper.getCustomerByPhoneNumber(clueDistributionLogEntity.PhoneNumber);

                    Integer customerID;
                    Integer followUpRecordID;
                    String employee;

                    if (customer == null) {
                        if (StringUtils.hasText(clueDistributionLogEntity.PhoneNumber)) {
                            Integer count = customerMapper.customerCount(clueDistributionLogEntity.PhoneNumber);
                            if (count > 0)
                                clueDistributionLogEntity.Log = "手机号已存在，不用新建客户";
                            clueDistributionLogEntity.Success = false;
                        }

                        CustomerEntity customerEntity = new CustomerEntity();
                        customerEntity.Name = clueDistributionLogEntity.Name;
                        customerEntity.PhoneNumber = clueDistributionLogEntity.PhoneNumber;
                        customerEntity.Gender = clueDistributionLogEntity.Gender;
                        //todo 会员编码后续完善
//                        customerEntity.Code = AutoNumberUtils.getCustomerCode();
                        customerEntity.CreatedOn = clueDistributionLogEntity.CreateTime;
                        customerEntity.IsMember = false;
                        customerEntity.IsLockMemberLevel = false;
                        //省份
                        String provinceCode = null;
                        String cityCode = null;
                        String areaCode = null;
                        if (StringUtils.hasText(clueDistributionLogEntity.Province)) {
                            provinceCode = clueDistributionMapper.getProvinceCode(clueDistributionLogEntity.Province);
                        }
                        if (StringUtils.hasText(clueDistributionLogEntity.City) && StringUtils.hasText(provinceCode)) {
                            //城市
                            cityCode = clueDistributionMapper.getCityCode(clueDistributionLogEntity.City, provinceCode);
                            if (StringUtils.hasText(clueDistributionLogEntity.County) && StringUtils.hasText(cityCode)) {
                                //区
                                areaCode = clueDistributionMapper.getAreaCode(clueDistributionLogEntity.County, cityCode);
                            }
                        }

                        if (StringUtils.hasText(provinceCode) && StringUtils.hasText(cityCode)) {
                            customerEntity.ProvinceCode = provinceCode;
                            customerEntity.CityCode = cityCode;
                            if (StringUtils.hasText(areaCode)){
                                customerEntity.AreaCode = areaCode;
                            }
                        }
                        customerEntity.Address = clueDistributionLogEntity.Address;

                        String employeeID = null;
                        //分配
                        if (StringUtils.hasText(provinceCode) && StringUtils.hasText(cityCode)) {
                            ClueDistributionEmployeeEntity employeeEntity = clueDistributionMapper.getClueDistribution(provinceCode, cityCode);
                            if (employeeEntity != null) {
                                employeeID = employeeEntity.EmployeeID;
                                clueDistributionMapper.updateClueDistributionEmployeeAddDistributionNum(employeeEntity.ID, employeeEntity.EmployeeID);
                            } else {
                                employeeID = clueDistributionMapper.getClueDistributionEmployeeConfig();
                            }
                        } else {
                            employeeID = clueDistributionMapper.getClueDistributionEmployeeConfig();
                        }

                        if (!StringUtils.hasText(employeeID)) {
                            clueDistributionLogEntity.Log = "分配员工未配置";
                            clueDistributionLogEntity.Success = false;
                        }

                        customerMapper.insertCustomer(customerEntity);

                        //更新门店 - 注释掉自动绑定客服所属门店的逻辑，改为在客户实际到店时再设置所属门店
                        //insertCustomerEntity(customerEntity.ID, clueDistributionMapper.getEmployeeEntity(employeeID), true, true);

                        //指派跟进记录
                        FollowUpRecordEntity followUpRecordEntity = new FollowUpRecordEntity();
                        followUpRecordEntity.CustomerID = customerEntity.ID;
                        followUpRecordEntity.FollowUpContent = "";
                        followUpRecordEntity.IsFollowUp = false;
                        followUpRecordEntity.FollowUpBy = employeeID;
                        followUpRecordEntity.PlannedOn = clueDistributionLogEntity.CreateTime;
                        followUpRecordEntity.PlannedRemark = clueDistributionLogEntity.Remark;
                        followUpRecordEntity.CreatedOn = clueDistributionLogEntity.CreateTime;
                        followUpRecordEntity.CreatedBy = employeeID;
                        followUpRecordEntity.LeadSource = LeadSourceEnum.DOUYIN_XINXILIU.getCode();
                        followUpMapper.createFollowUpRecord(followUpRecordEntity);

                        customerID = customerEntity.ID;
                        followUpRecordID = followUpRecordEntity.ID;
                        employee = employeeID;
                    } else {
                        //省份
                        String provinceCode = null;
                        String cityCode = null;
                        String areaCode = null;
                        if (StringUtils.hasText(clueDistributionLogEntity.Province)) {
                            provinceCode = clueDistributionMapper.getProvinceCode(clueDistributionLogEntity.Province);
                        }
                        if (StringUtils.hasText(clueDistributionLogEntity.City) && StringUtils.hasText(provinceCode)) {
                            //城市
                            cityCode = clueDistributionMapper.getCityCode(clueDistributionLogEntity.City, provinceCode);
                            if (StringUtils.hasText(clueDistributionLogEntity.County) && StringUtils.hasText(cityCode)) {
                                //区
                                areaCode = clueDistributionMapper.getAreaCode(clueDistributionLogEntity.County, cityCode);
                            }
                        }

                        if (StringUtils.hasText(provinceCode) && StringUtils.hasText(cityCode)) {
                            customer.ProvinceCode = provinceCode;
                            customer.CityCode = cityCode;
                            if (StringUtils.hasText(areaCode)){
                                customer.AreaCode = areaCode;
                            }
                        }

                        customerMapper.updateCustomer(customer);

                        String employeeID = null;
                        //分配
                        if (StringUtils.hasText(provinceCode) && StringUtils.hasText(cityCode)) {
                            ClueDistributionEmployeeEntity employeeEntity = clueDistributionMapper.getClueDistribution(provinceCode, cityCode);
                            if (employeeEntity != null) {
                                employeeID = employeeEntity.EmployeeID;
                                clueDistributionMapper.updateClueDistributionEmployeeAddDistributionNum(employeeEntity.ID, employeeEntity.EmployeeID);
                            } else {
                                employeeID = clueDistributionMapper.getClueDistributionEmployeeConfig();
                            }
                        } else {
                            employeeID = clueDistributionMapper.getClueDistributionEmployeeConfig();
                        }

                        if (!StringUtils.hasText(employeeID)) {
                            clueDistributionLogEntity.Log = "分配员工未配置";
                            clueDistributionLogEntity.Success = false;
                        }
                        //指派跟进记录
                        FollowUpRecordEntity followUpRecordEntity = new FollowUpRecordEntity();
                        followUpRecordEntity.CustomerID = customer.ID;
                        followUpRecordEntity.FollowUpContent = "";
                        followUpRecordEntity.IsFollowUp = false;
                        followUpRecordEntity.FollowUpBy = employeeID;
                        followUpRecordEntity.PlannedOn = clueDistributionLogEntity.CreateTime;
                        followUpRecordEntity.PlannedRemark = clueDistributionLogEntity.Remark;
                        followUpRecordEntity.CreatedOn = clueDistributionLogEntity.CreateTime;
                        followUpRecordEntity.CreatedBy = employeeID;
                        followUpRecordEntity.LeadSource = LeadSourceEnum.DOUYIN_XINXILIU.getCode();
                        followUpMapper.createFollowUpRecord(followUpRecordEntity);

                        customerID = customer.ID;
                        followUpRecordID = followUpRecordEntity.ID;
                        employee = employeeID;
                    }

                    clueDistributionLogEntity.CustomerID = customerID;
                    clueDistributionLogEntity.FollowUpRecordID = followUpRecordID;
                    clueDistributionLogEntity.EmployeeID = employee;

                    clueDistributionLogEntity.Log = "成功";
                    clueDistributionLogEntity.Success = true;

                    clueDistributionMapper.addClueDistributionLogEntity(clueDistributionLogEntity);
                }
            }
        }
    }

    private void insertCustomerEntity(Integer CustomerID, Integer EntityID, Boolean IsBelongEntity, Boolean IsCreateEntity) {
        if (CustomerID != null) {
            Integer ID = customerMapper.getCustomerEntityID(CustomerID, EntityID);
            if (ID == null) {
                CustomerEntityEntity customerEntityEntity = new CustomerEntityEntity();
                customerEntityEntity.CustomerID = CustomerID;
                customerEntityEntity.EntityID = EntityID;
                customerEntityEntity.IsBelongEntity = IsBelongEntity;
                customerEntityEntity.IsCreateEntity = IsCreateEntity;
                customerMapper.insertCustomerEntity(customerEntityEntity);

            }
        }
    }

    /**
     * 拉取腾讯线索信息
     * @return 调用结果
     */
    @Transactional
    public BaseOutput fetchTencentLeads() {
        // 腾讯线索API配置信息
        String url = "https://leads.qq.com/api/mv1/leads/list";
        String token = "654551801753410181";
        String secret = "95f8429879aec3b7d8936fd19725658d";

        try {
            // 计算时间范围（最近5天）
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE, -5);
            Date startDate = cal.getTime();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String startTime = sdf.format(startDate);
            String endTime = sdf.format(new Date());

            // 生成签名
            Long timestamp = System.currentTimeMillis() / 1000;
            String nonce = String.valueOf(System.currentTimeMillis());
            String signStr = token + "." + timestamp + "." + secret;
            String sign = md5(signStr);
            String signature = java.util.Base64.getEncoder().encodeToString((token + "," + timestamp + "," + nonce + "," + sign).getBytes());

            // 构建请求URL
            String requestUrl = url + "?start_time=" + startTime + "&end_time=" + endTime + "&page=1&page_size=50";

            // 构造GET请求
            HttpEntityEnclosingRequestBase httpEntity = new HttpEntityEnclosingRequestBase() {
                @Override
                public String getMethod() {
                    return "GET";
                }
            };

            httpEntity.setURI(URI.create(requestUrl));
            httpEntity.setHeader("X-Signature", signature);
            httpEntity.setHeader("X-Signature-Algorithm", "SHA1");
            httpEntity.setHeader("Accept", "application/json");
            httpEntity.setHeader("Content-Type", "application/json");

            CloseableHttpResponse response = null;
            CloseableHttpClient client = null;

            try {
                client = HttpClientBuilder.create().build();
                response = client.execute(httpEntity);

                if (response != null && response.getStatusLine().getStatusCode() == 200) {
                    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), "UTF-8"));
                    StringBuffer result = new StringBuffer();
                    String line = "";
                    while ((line = bufferedReader.readLine()) != null) {
                        result.append(line);
                    }
                    bufferedReader.close();

                    JSONObject jsonObject = JSONObject.parseObject(result.toString());
                    logger.info("腾讯线索拉取结果：{}", jsonObject);

                    if (jsonObject != null && jsonObject.getInteger("code") == 0) {
                        // 处理返回的线索数据
                        processTencentLeads(jsonObject);
                        return BaseOutput.success("腾讯线索拉取成功");
                    } else {
                        String message = jsonObject != null ? jsonObject.getString("message") : "未知错误";
                        return BaseOutput.failed("腾讯线索拉取失败：" + message);
                    }
                } else {
                    return BaseOutput.failed("腾讯线索拉取失败：HTTP状态码 " + (response != null ? response.getStatusLine().getStatusCode() : "null"));
                }

            } catch (ClientProtocolException e) {
                logger.error("腾讯线索拉取异常：", e);
                return BaseOutput.failed("腾讯线索拉取异常：" + e.getMessage());
            } catch (IOException e) {
                logger.error("腾讯线索拉取IO异常：", e);
                return BaseOutput.failed("腾讯线索拉取IO异常：" + e.getMessage());
            } finally {
                try {
                    if (response != null) {
                        response.close();
                    }
                    if (client != null) {
                        client.close();
                    }
                } catch (IOException e) {
                    logger.error("关闭HTTP连接异常：", e);
                }
            }

        } catch (Exception e) {
            logger.error("腾讯线索拉取异常：", e);
            return BaseOutput.failed("腾讯线索拉取异常：" + e.getMessage());
        }
    }

    /**
     * 处理腾讯线索数据
     * @param jsonObject 腾讯API返回的数据
     */
    private void processTencentLeads(JSONObject jsonObject) {
        try {
            JSONObject data = jsonObject.getJSONObject("data");
            if (data != null) {
                JSONArray list = data.getJSONArray("list");
                if (list != null && list.size() > 0) {
                    logger.info("处理腾讯线索数据，共{}条", list.size());
                    saveTencentLeads(list);
                }
            }
        } catch (Exception e) {
            logger.error("处理腾讯线索数据异常：", e);
        }
    }

    /**
     * 保存腾讯线索数据（模仿save方法）
     * @param data 腾讯线索数据数组
     */
    @Transactional
    public synchronized void saveTencentLeads(JSONArray data) {
        if (data != null) {
            for (int i = 0; i < data.size(); i++) {
                Object arrayElement = data.get(i);
                JSONObject jsonObject = new JSONObject((Map<String, Object>) arrayElement);

                // 检查线索是否已存在
                ClueDistributionLogEntity clueDistributionLog = clueDistributionMapper.getClueDistributionLogByClueID(jsonObject.getString("leads_id"));

                if (clueDistributionLog == null) {
                    ClueDistributionLogEntity clueDistributionLogEntity = new ClueDistributionLogEntity();
                    clueDistributionLogEntity.ClueID = jsonObject.getString("leads_id");
                    clueDistributionLogEntity.AppID = "TENCENT"; // 腾讯线索标识
                    clueDistributionLogEntity.Name = jsonObject.getString("leads_name");
                    clueDistributionLogEntity.PhoneNumber = jsonObject.getString("leads_tel");

                    // 处理性别
                    String gender = jsonObject.getString("leads_gender");
                    if ("GENDER_TYPE_MALE".equals(gender)) {
                        clueDistributionLogEntity.Gender = "男";
                    } else if ("GENDER_TYPE_FEMALE".equals(gender)) {
                        clueDistributionLogEntity.Gender = "女";
                    } else {
                        clueDistributionLogEntity.Gender = "未知";
                    }

                    // 处理地址信息
                    String leadsArea = jsonObject.getString("leads_area");
                    if (StringUtils.hasText(leadsArea)) {
                        // 解析地址信息，格式可能是"深圳市南山区"
                        String[] areaParts = leadsArea.split("市|区|县");
                        if (areaParts.length >= 1) {
                            clueDistributionLogEntity.Province = areaParts[0];
                        }
                        if (areaParts.length >= 2) {
                            clueDistributionLogEntity.City = areaParts[0] + "市";
                        }
                    }

                    clueDistributionLogEntity.County = jsonObject.getString("tel_location"); // 号码归属地
                    clueDistributionLogEntity.Address = jsonObject.getString("address");
                    clueDistributionLogEntity.Remark = jsonObject.getString("bundle"); // 其他字段信息

                    // 处理创建时间
                    try {
                        String createTimeStr = jsonObject.getString("leads_create_time");
                        if (StringUtils.hasText(createTimeStr)) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            clueDistributionLogEntity.CreateTime = sdf.parse(createTimeStr);
                        } else {
                            clueDistributionLogEntity.CreateTime = new Date();
                        }
                    } catch (ParseException e) {
                        logger.error("解析腾讯线索创建时间异常：", e);
                        clueDistributionLogEntity.CreateTime = new Date();
                    }

                    // 数据验证
                    if (StringUtil.isEmpty(clueDistributionLogEntity.Name)) {
                        clueDistributionLogEntity.Log = "失败：姓名为空";
                        clueDistributionLogEntity.Success = false;
                    } else if (StringUtil.isEmpty(clueDistributionLogEntity.Gender)) {
                        clueDistributionLogEntity.Log = "失败：性别为空";
                        clueDistributionLogEntity.Success = false;
                    } else if (StringUtil.isEmpty(clueDistributionLogEntity.PhoneNumber)) {
                        clueDistributionLogEntity.Log = "失败：手机号为空";
                        clueDistributionLogEntity.Success = false;
                    } else if (clueDistributionLogEntity.CreateTime == null) {
                        clueDistributionLogEntity.Log = "失败：创建时间为空";
                        clueDistributionLogEntity.Success = false;
                    }

                    // 处理客户信息（参考save方法逻辑）
                    CustomerEntity customer = customerMapper.getCustomerByPhoneNumber(clueDistributionLogEntity.PhoneNumber);

                    Integer customerID;
                    Integer followUpRecordID;
                    String employee;

                    if (customer == null) {
                        // 检查手机号是否已存在
                        if (StringUtils.hasText(clueDistributionLogEntity.PhoneNumber)) {
                            Integer count = customerMapper.customerCount(clueDistributionLogEntity.PhoneNumber);
                            if (count > 0) {
                                clueDistributionLogEntity.Log = "手机号已存在，不用新建客户";
                                clueDistributionLogEntity.Success = false;
                            }
                        }

                        // 创建新客户
                        CustomerEntity customerEntity = new CustomerEntity();
                        customerEntity.Name = clueDistributionLogEntity.Name;
                        customerEntity.PhoneNumber = clueDistributionLogEntity.PhoneNumber;
                        customerEntity.Gender = clueDistributionLogEntity.Gender;
                        customerEntity.CreatedOn = clueDistributionLogEntity.CreateTime;
                        customerEntity.IsMember = false;
                        customerEntity.IsLockMemberLevel = false;

                        // 处理省市区信息
                        String provinceCode = null;
                        String cityCode = null;
                        String areaCode = null;
                        if (StringUtils.hasText(clueDistributionLogEntity.Province)) {
                            provinceCode = clueDistributionMapper.getProvinceCode(clueDistributionLogEntity.Province);
                        }
                        if (StringUtils.hasText(clueDistributionLogEntity.City) && StringUtils.hasText(provinceCode)) {
                            cityCode = clueDistributionMapper.getCityCode(clueDistributionLogEntity.City, provinceCode);
                            if (StringUtils.hasText(clueDistributionLogEntity.County) && StringUtils.hasText(cityCode)) {
                                areaCode = clueDistributionMapper.getAreaCode(clueDistributionLogEntity.County, cityCode);
                            }
                        }

                        if (StringUtils.hasText(provinceCode) && StringUtils.hasText(cityCode)) {
                            customerEntity.ProvinceCode = provinceCode;
                            customerEntity.CityCode = cityCode;
                            if (StringUtils.hasText(areaCode)) {
                                customerEntity.AreaCode = areaCode;
                            }
                        }
                        customerEntity.Address = clueDistributionLogEntity.Address;

                        // 分配员工
                        String employeeID = null;
                        if (StringUtils.hasText(provinceCode) && StringUtils.hasText(cityCode)) {
                            ClueDistributionEmployeeEntity employeeEntity = clueDistributionMapper.getClueDistribution(provinceCode, cityCode);
                            if (employeeEntity != null) {
                                employeeID = employeeEntity.EmployeeID;
                                clueDistributionMapper.updateClueDistributionEmployeeAddDistributionNum(employeeEntity.ID, employeeEntity.EmployeeID);
                            } else {
                                employeeID = clueDistributionMapper.getClueDistributionEmployeeConfig();
                            }
                        } else {
                            employeeID = clueDistributionMapper.getClueDistributionEmployeeConfig();
                        }

                        if (!StringUtils.hasText(employeeID)) {
                            clueDistributionLogEntity.Log = "分配员工未配置";
                            clueDistributionLogEntity.Success = false;
                        }

                        customerMapper.insertCustomer(customerEntity);

                        // 创建跟进记录
                        FollowUpRecordEntity followUpRecordEntity = new FollowUpRecordEntity();
                        followUpRecordEntity.CustomerID = customerEntity.ID;
                        followUpRecordEntity.FollowUpContent = "";
                        followUpRecordEntity.IsFollowUp = false;
                        followUpRecordEntity.FollowUpBy = employeeID;
                        followUpRecordEntity.PlannedOn = clueDistributionLogEntity.CreateTime;
                        followUpRecordEntity.PlannedRemark = clueDistributionLogEntity.Remark;
                        followUpRecordEntity.CreatedOn = clueDistributionLogEntity.CreateTime;
                        followUpRecordEntity.CreatedBy = employeeID;
                        followUpRecordEntity.LeadSource = LeadSourceEnum.WEIXIN_PENGYOUQUAN.getCode(); // 微信朋友圈
                        followUpMapper.createFollowUpRecord(followUpRecordEntity);

                        customerID = customerEntity.ID;
                        followUpRecordID = followUpRecordEntity.ID;
                        employee = employeeID;
                    } else {
                        // 更新现有客户信息
                        String provinceCode = null;
                        String cityCode = null;
                        String areaCode = null;
                        if (StringUtils.hasText(clueDistributionLogEntity.Province)) {
                            provinceCode = clueDistributionMapper.getProvinceCode(clueDistributionLogEntity.Province);
                        }
                        if (StringUtils.hasText(clueDistributionLogEntity.City) && StringUtils.hasText(provinceCode)) {
                            cityCode = clueDistributionMapper.getCityCode(clueDistributionLogEntity.City, provinceCode);
                            if (StringUtils.hasText(clueDistributionLogEntity.County) && StringUtils.hasText(cityCode)) {
                                areaCode = clueDistributionMapper.getAreaCode(clueDistributionLogEntity.County, cityCode);
                            }
                        }

                        if (StringUtils.hasText(provinceCode) && StringUtils.hasText(cityCode)) {
                            customer.ProvinceCode = provinceCode;
                            customer.CityCode = cityCode;
                            if (StringUtils.hasText(areaCode)) {
                                customer.AreaCode = areaCode;
                            }
                        }

                        customerMapper.updateCustomer(customer);

                        // 分配员工
                        String employeeID = null;
                        if (StringUtils.hasText(provinceCode) && StringUtils.hasText(cityCode)) {
                            ClueDistributionEmployeeEntity employeeEntity = clueDistributionMapper.getClueDistribution(provinceCode, cityCode);
                            if (employeeEntity != null) {
                                employeeID = employeeEntity.EmployeeID;
                                clueDistributionMapper.updateClueDistributionEmployeeAddDistributionNum(employeeEntity.ID, employeeEntity.EmployeeID);
                            } else {
                                employeeID = clueDistributionMapper.getClueDistributionEmployeeConfig();
                            }
                        } else {
                            employeeID = clueDistributionMapper.getClueDistributionEmployeeConfig();
                        }

                        if (!StringUtils.hasText(employeeID)) {
                            clueDistributionLogEntity.Log = "分配员工未配置";
                            clueDistributionLogEntity.Success = false;
                        }

                        // 创建跟进记录
                        FollowUpRecordEntity followUpRecordEntity = new FollowUpRecordEntity();
                        followUpRecordEntity.CustomerID = customer.ID;
                        followUpRecordEntity.FollowUpContent = "";
                        followUpRecordEntity.IsFollowUp = false;
                        followUpRecordEntity.FollowUpBy = employeeID;
                        followUpRecordEntity.PlannedOn = clueDistributionLogEntity.CreateTime;
                        followUpRecordEntity.PlannedRemark = clueDistributionLogEntity.Remark;
                        followUpRecordEntity.CreatedOn = clueDistributionLogEntity.CreateTime;
                        followUpRecordEntity.CreatedBy = employeeID;
                        followUpRecordEntity.LeadSource = LeadSourceEnum.WEIXIN_PENGYOUQUAN.getCode(); // 微信朋友圈
                        followUpMapper.createFollowUpRecord(followUpRecordEntity);

                        customerID = customer.ID;
                        followUpRecordID = followUpRecordEntity.ID;
                        employee = employeeID;
                    }

                    clueDistributionLogEntity.CustomerID = customerID;
                    clueDistributionLogEntity.FollowUpRecordID = followUpRecordID;
                    clueDistributionLogEntity.EmployeeID = employee;

                    clueDistributionLogEntity.Log = "成功";
                    clueDistributionLogEntity.Success = true;

                    clueDistributionMapper.addClueDistributionLogEntity(clueDistributionLogEntity);
                }
            }
        }
    }

    /**
     * MD5加密
     * @param input 输入字符串
     * @return MD5加密后的字符串
     */
    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }
}
